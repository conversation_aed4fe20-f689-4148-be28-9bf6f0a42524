# 🚀 Chotot House Rent Crawler - Deployment Guide

This comprehensive guide covers the complete deployment process for the Chotot House Rent Crawler application on a production server.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Server Requirements](#server-requirements)
3. [Quick Start](#quick-start)
4. [Deployment Methods](#deployment-methods)
5. [Configuration](#configuration)
6. [Process Management](#process-management)
7. [Monitoring & Maintenance](#monitoring--maintenance)
8. [Troubleshooting](#troubleshooting)
9. [Security Considerations](#security-considerations)
10. [Backup & Recovery](#backup--recovery)

## 🔧 Prerequisites

### Required Software

- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **Node.js**: Version 18.x or higher
- **Git**: For repository management
- **Docker**: (Optional) For containerized deployment
- **PM2**: (Optional) For process management

### Required Accounts & Services

- **Slack Workspace**: For notifications
- **Server Access**: SSH access with sudo privileges
- **Domain/IP**: For accessing the application (optional)

## 🖥️ Server Requirements

### Minimum Requirements

- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB free space
- **Network**: Stable internet connection

### Recommended Requirements

- **CPU**: 4 cores
- **RAM**: 8GB
- **Storage**: 50GB free space
- **Network**: High-speed internet connection

### System Dependencies

The application requires Chrome/Chromium for web scraping:

- Chrome browser dependencies
- X11 libraries (for headless Chrome)
- Font packages

## 🚀 Quick Start

### Option 1: Automated Deployment (Recommended)

1. **Download the deployment script**:

   ```bash
   wget https://raw.githubusercontent.com/your-username/chotot-house-rent/main/scripts/deploy.sh
   chmod +x deploy.sh
   ```

2. **Run initial server setup**:

   ```bash
   sudo ./deploy.sh setup
   ```

3. **Deploy the application**:

   ```bash
   # For Docker deployment
   sudo ./deploy.sh docker

   # OR for native deployment
   sudo ./deploy.sh native
   ```

### Option 2: Manual Deployment

Follow the detailed steps in the [Deployment Methods](#deployment-methods) section.

## 🐳 Deployment Methods

### Method 1: Docker Deployment (Recommended)

Docker deployment provides isolation, easy management, and consistent environments.

#### Step 1: Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application user
sudo useradd -r -s /bin/bash -d /usr/src/app crawler
sudo mkdir -p /usr/src/app
sudo chown -R crawler:crawler /usr/src/app
```

#### Step 2: Clone Repository

```bash
sudo -u crawler git clone https://github.com/your-username/chotot-house-rent.git /usr/src/app
cd /usr/src/app
```

#### Step 3: Configure Environment

```bash
# Copy environment file
sudo -u crawler cp .env.example .env

# Edit configuration (see Configuration section)
sudo -u crawler nano .env
```

#### Step 4: Deploy with Docker

```bash
# Build and start containers
sudo docker-compose up -d

# Check status
sudo docker-compose ps
sudo docker-compose logs -f
```

### Method 2: Native Deployment

Native deployment runs the application directly on the server using PM2.

#### Step 1: Install Node.js

```bash
# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PM2 globally
sudo npm install -g pm2
```

#### Step 2: Setup Application

```bash
# Create user and directories
sudo useradd -r -s /bin/bash -d /usr/src/app crawler
sudo mkdir -p /usr/src/app
sudo chown -R crawler:crawler /usr/src/app

# Clone repository
sudo -u crawler git clone https://github.com/your-username/chotot-house-rent.git /usr/src/app
cd /usr/src/app

# Install dependencies
sudo -u crawler npm ci --only=production
```

#### Step 3: Configure Environment

```bash
# Setup configuration files
sudo -u crawler cp .env.example .env
sudo -u crawler cp urls.example.json urls.json
sudo -u crawler cp proxy.config.example.js proxy.config.js
sudo -u crawler cp captcha.config.example.js captcha.config.js

# Edit configuration
sudo -u crawler nano .env
```

#### Step 4: Start with PM2

```bash
# Start application
sudo -u crawler pm2 start ecosystem.config.js --env production

# Save PM2 configuration
sudo -u crawler pm2 save

# Setup startup script
sudo pm2 startup
```

## ⚙️ Configuration

### Environment Variables (.env)

Create and configure your `.env` file based on `.env.example`:

```bash
# Required: Slack webhook URL for notifications
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/YOUR/WEBHOOK/URL

# Application settings
NODE_ENV=production
TZ=Asia/Ho_Chi_Minh

# Crawler configuration
CRAWLER_BASE_URL=https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000
CRAWLER_MAX_PAGES=5
CRAWLER_SCHEDULE=0 */4 * * *

# Browser settings
HEADLESS=true
BROWSER_TIMEOUT=30000

# Optional: Proxy settings
PROXY_ENABLED=false
PROXY_HOST=your-proxy-host.com
PROXY_PORT=8080
PROXY_USERNAME=your-username
PROXY_PASSWORD=your-password

# Optional: CAPTCHA settings
CAPTCHA_ENABLED=false
TWOCAPTCHA_API_KEY=your-api-key
```

### Slack Integration Setup

1. **Create Slack App**:

   - Go to https://api.slack.com/apps
   - Click "Create New App"
   - Choose "From scratch"
   - Name your app and select workspace

2. **Enable Incoming Webhooks**:

   - Go to "Incoming Webhooks"
   - Turn on "Activate Incoming Webhooks"
   - Click "Add New Webhook to Workspace"
   - Select channel and authorize

3. **Copy Webhook URL**:
   - Copy the webhook URL to your `.env` file

### URL Configuration

Edit `urls.json` to specify crawling targets:

```json
{
  "site": [
    {
      "name": "Chotot District 7",
      "url": "https://www.nhatot.com/thue-bat-dong-san-quan-7-tp-ho-chi-minh?price=5000000-8000000"
    }
  ]
}
```

## 🔄 Process Management

### Using Docker

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# Restart services
docker-compose restart

# View logs
docker-compose logs -f

# Update application
docker-compose pull
docker-compose up -d --force-recreate
```

### Using PM2

```bash
# Start application
pm2 start ecosystem.config.js --env production

# Stop application
pm2 stop chotot-crawler

# Restart application
pm2 restart chotot-crawler

# View logs
pm2 logs chotot-crawler

# Monitor processes
pm2 monit

# Update application
git pull
npm ci --only=production
pm2 restart chotot-crawler
```

### Using Process Manager Script

The included process manager script provides unified commands:

```bash
# Start with PM2
./scripts/process-manager.sh pm2 start

# Start with systemd
sudo ./scripts/process-manager.sh systemd start

# Auto-detect and start
./scripts/process-manager.sh auto start

# View status
./scripts/process-manager.sh auto status

# View logs
./scripts/process-manager.sh auto logs
```

## 📊 Monitoring & Maintenance

### Health Monitoring

Use the included monitoring script:

```bash
# Run health check
./scripts/monitor.sh check

# Full monitoring cycle
./scripts/monitor.sh full

# Setup cron job for automated monitoring
echo "*/15 * * * * /usr/src/app/scripts/monitor.sh full" | sudo crontab -
```

### Log Management

Logs are stored in the following locations:

- **Application logs**: `./logs/`
- **PM2 logs**: `./logs/pm2-*.log`
- **Docker logs**: `docker-compose logs`

### Automated Cleanup

Setup automated cleanup with cron:

```bash
# Add to crontab
sudo crontab -e

# Add these lines:
# Clean old logs daily at 2 AM
0 2 * * * /usr/src/app/scripts/monitor.sh cleanup

# Backup data weekly on Sunday at 1 AM
0 1 * * 0 cd /usr/src/app && npm run backup:data
```

### Performance Monitoring

Monitor system resources:

```bash
# Check memory usage
free -h

# Check disk usage
df -h

# Check CPU usage
top

# Check application-specific resources
docker stats  # For Docker deployment
pm2 monit     # For PM2 deployment
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Application Won't Start

**Symptoms**: Process exits immediately or fails to start

**Solutions**:

```bash
# Check logs for errors
docker-compose logs  # Docker
pm2 logs chotot-crawler  # PM2

# Verify configuration
node -e "console.log(require('./src/config.loader'))"

# Check environment variables
printenv | grep -E "(SLACK|NODE_ENV|CRAWLER)"

# Test Slack connection
npm test
```

#### 2. Chrome/Puppeteer Issues

**Symptoms**: Browser launch failures, timeout errors

**Solutions**:

```bash
# Install missing dependencies
sudo apt-get update
sudo apt-get install -y \
    libnss3 libatk-bridge2.0-0 libdrm2 libxkbcommon0 \
    libgtk-3-0 libatspi2.0-0 libxss1 libgconf-2-4

# Check Chrome installation
google-chrome --version
chromium-browser --version

# Test browser launch
node -e "const puppeteer = require('puppeteer'); puppeteer.launch().then(browser => { console.log('Browser launched successfully'); browser.close(); })"
```

#### 3. Memory Issues

**Symptoms**: Out of memory errors, slow performance

**Solutions**:

```bash
# Increase swap space
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile

# Optimize Chrome flags in config
# Add to browser.args in config:
"--memory-pressure-off",
"--max_old_space_size=2048"

# Monitor memory usage
watch -n 1 'free -m'
```

#### 4. Network/Proxy Issues

**Symptoms**: Connection timeouts, blocked requests

**Solutions**:

```bash
# Test direct connection
curl -I https://www.nhatot.com

# Test with proxy
curl -I --proxy http://proxy:port https://www.nhatot.com

# Check proxy configuration
cat proxy.config.js

# Verify DNS resolution
nslookup nhatot.com
```

#### 5. Slack Notifications Not Working

**Symptoms**: No Slack messages received

**Solutions**:

```bash
# Test webhook URL
curl -X POST -H 'Content-type: application/json' \
    --data '{"text":"Test message"}' \
    $SLACK_WEBHOOK_URL

# Check environment variable
echo $SLACK_WEBHOOK_URL

# Verify Slack app permissions
# Check webhook URL in Slack app settings
```

### Debug Mode

Enable debug mode for troubleshooting:

```bash
# Set debug environment variables
export DEBUG=*
export HEADLESS=false
export LOG_LEVEL=debug

# Run in debug mode
npm run dev
```

### Log Analysis

Analyze logs for issues:

```bash
# Search for errors
grep -i error logs/*.log

# Search for specific patterns
grep -i "timeout\|failed\|exception" logs/*.log

# Monitor logs in real-time
tail -f logs/crawler.log

# Analyze PM2 logs
pm2 logs --lines 100
```

## 🔒 Security Considerations

### Server Security

1. **Firewall Configuration**:

```bash
# Enable UFW firewall
sudo ufw enable

# Allow SSH
sudo ufw allow ssh

# Allow specific ports if needed
sudo ufw allow 3000  # If web interface is enabled
```

2. **User Permissions**:

```bash
# Run application as non-root user
sudo useradd -r -s /bin/bash crawler

# Set proper file permissions
sudo chown -R crawler:crawler /usr/src/app
sudo chmod -R 755 /usr/src/app
sudo chmod 600 /usr/src/app/.env
```

3. **SSH Security**:

```bash
# Disable root login
sudo sed -i 's/PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config

# Use SSH keys instead of passwords
# Disable password authentication
sudo sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config

sudo systemctl restart ssh
```

### Application Security

1. **Environment Variables**:

   - Never commit `.env` files to version control
   - Use strong, unique API keys
   - Rotate credentials regularly

2. **Network Security**:

   - Use HTTPS for all external connections
   - Validate SSL certificates
   - Use reputable proxy services if needed

3. **Data Protection**:
   - Encrypt sensitive data at rest
   - Implement proper access controls
   - Regular security updates

## 💾 Backup & Recovery

### Automated Backups

Setup automated backups:

```bash
# Create backup script
cat > /usr/local/bin/backup-chotot.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/var/backups/chotot-crawler"
DATE=$(date +%Y%m%d-%H%M%S)
APP_DIR="/usr/src/app"

mkdir -p "$BACKUP_DIR"
cd "$APP_DIR"

# Create backup
tar -czf "$BACKUP_DIR/backup-$DATE.tar.gz" \
    data/ output/ sessions/ .env urls.json \
    proxy.config.js captcha.config.js

# Keep only last 7 backups
find "$BACKUP_DIR" -name "backup-*.tar.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_DIR/backup-$DATE.tar.gz"
EOF

chmod +x /usr/local/bin/backup-chotot.sh

# Add to crontab
echo "0 2 * * * /usr/local/bin/backup-chotot.sh" | sudo crontab -
```

### Manual Backup

```bash
# Create manual backup
cd /usr/src/app
npm run backup:data

# Or create custom backup
tar -czf backup-$(date +%Y%m%d).tar.gz \
    data/ output/ sessions/ .env urls.json
```

### Recovery Process

```bash
# Stop application
docker-compose down  # Docker
pm2 stop chotot-crawler  # PM2

# Restore from backup
cd /usr/src/app
tar -xzf /var/backups/chotot-crawler/backup-YYYYMMDD-HHMMSS.tar.gz

# Restart application
docker-compose up -d  # Docker
pm2 restart chotot-crawler  # PM2
```

## 🔄 Updates & Maintenance

### Application Updates

```bash
# Using deployment script
sudo ./scripts/deploy.sh update

# Manual update process
cd /usr/src/app
git pull origin main
npm ci --only=production

# Restart application
docker-compose restart  # Docker
pm2 restart chotot-crawler  # PM2
```

### System Updates

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Node.js (if needed)
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Update Docker (if needed)
sudo apt-get update
sudo apt-get install docker-ce docker-ce-cli containerd.io
```

### Maintenance Schedule

Recommended maintenance schedule:

- **Daily**: Monitor logs and system resources
- **Weekly**: Review crawled data and performance
- **Monthly**: Update system packages and dependencies
- **Quarterly**: Security audit and configuration review

## 📞 Support & Resources

### Getting Help

1. **Check logs first**: Most issues can be diagnosed from logs
2. **Review configuration**: Verify all settings are correct
3. **Test components**: Test Slack, browser, network connectivity separately
4. **Monitor resources**: Check CPU, memory, disk usage

### Useful Commands

```bash
# Quick status check
./scripts/monitor.sh check

# View recent logs
tail -n 100 logs/crawler.log

# Test configuration
npm run test-config

# Manual crawl test
npm run crawl

# Process management
./scripts/process-manager.sh auto status
```

### Performance Optimization

1. **Adjust crawling delays**: Increase delays if getting blocked
2. **Optimize Chrome flags**: Add memory and performance flags
3. **Use SSD storage**: For better I/O performance
4. **Monitor and tune**: Regular performance monitoring

---

## 🎉 Conclusion

This deployment guide provides comprehensive instructions for deploying the Chotot House Rent Crawler in production. Choose the deployment method that best fits your infrastructure and requirements.

For additional support or questions, refer to the application logs and monitoring tools provided. Regular maintenance and monitoring will ensure optimal performance and reliability.

**Happy crawling! 🏠🔍**
